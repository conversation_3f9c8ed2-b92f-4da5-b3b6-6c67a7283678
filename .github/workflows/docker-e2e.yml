name: NestJS API CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      # <database-relational-block>
      - name: Run e2e tests for NestJS with TypeORM
        id: relational
        run: docker compose -f docker-compose.relational.ci.yaml --env-file env-example-relational -p ci-relational up --build --exit-code-from api

      - name: Copy prod.log from container to host
        if: ${{ failure() && steps.relational.conclusion == 'failure' }}
        run: docker cp ci-relational-api-1:/usr/src/app/prod.log .
      # </database-relational-block>

      

      - name: Upload prod.log to artifacts for debugging
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: prod-logs
          path: prod.log
