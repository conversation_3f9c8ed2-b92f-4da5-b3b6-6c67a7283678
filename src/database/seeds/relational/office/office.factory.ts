import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OfficeEntity } from '../../../../offices/infrastructure/persistence/relational/entities/office.entity';
import { CityEntity } from '../../../../cities/infrastructure/persistence/relational/entities/city.entity';
import { CountryEntity } from '../../../../countries/infrastructure/persistence/relational/entities/country.entity';
import { UserEntity } from '../../../../users/infrastructure/persistence/relational/entities/user.entity';
import { FileEntity } from '../../../../files/infrastructure/persistence/relational/entities/file.entity';
import { OfficeRateEntity } from '../../../../office-rates/infrastructure/persistence/relational/entities/office-rate.entity';
import { CurrencyEntity } from '../../../../currencies/infrastructure/persistence/relational/entities/currency.entity';
import { WorkingHourEntity } from '../../../../working-hours/infrastructure/persistence/relational/entities/working-hour.entity';
import * as Papa from 'papaparse';
import * as fs from 'fs';
import { faker } from '@faker-js/faker';

interface CSVOfficeData {
  Ville: string;
  'Bureau de change': string;
  Adresse: string;
  'Code postal': string;
  Téléphone: string;
  Lundi: string;
  Mardi: string;
  Mercredi: string;
  Jeudi: string;
  Vendredi: string;
  Samedi: string;
  Dimanche: string;
  Lien: string;
  'Site Web/Email': string;
  Longitude: string;
  Latitude: string;
  'Image 1': string;
  'Image 2': string;
  'Image 3': string;
}

@Injectable()
export class CSVOfficeFactory {
  constructor(
    @InjectRepository(OfficeEntity)
    private repositoryOffice: Repository<OfficeEntity>,
    @InjectRepository(CityEntity)
    private repositoryCity: Repository<CityEntity>,
    @InjectRepository(CountryEntity)
    private repositoryCountry: Repository<CountryEntity>,
    @InjectRepository(UserEntity)
    private repositoryUser: Repository<UserEntity>,
    @InjectRepository(FileEntity)
    private repositoryFile: Repository<FileEntity>,
    @InjectRepository(CurrencyEntity)
    private repositoryCurrency: Repository<CurrencyEntity>,
    @InjectRepository(OfficeRateEntity)
    private repositoryOfficeRate: Repository<OfficeRateEntity>,
    @InjectRepository(WorkingHourEntity)
    private repositoryWorkingHour: Repository<WorkingHourEntity>,
  ) {}

  private parseCSVData(csvFilePath: string): Promise<CSVOfficeData[]> {
    return new Promise((resolve, reject) => {
      const csvFile = fs.readFileSync(csvFilePath, 'utf8');

      Papa.parse(csvFile, {
        header: true,
        skipEmptyLines: true,
        dynamicTyping: false,
        delimiter: ',',
        complete: (results) => {
          if (results.errors.length > 0) {
            console.error('CSV parsing errors:', results.errors);
          }
          resolve(results.data as CSVOfficeData[]);
        },
        error: (error) => {
          reject(error);
        },
      });
    });
  }

  private parseWorkingHours(timeString: string): {
    startTime: string;
    endTime: string;
    isActive: boolean;
  } {
    if (
      !timeString ||
      timeString.toLowerCase() === 'fermé' ||
      timeString.toLowerCase() === 'closed'
    ) {
      return {
        startTime: '00:00',
        endTime: '00:00',
        isActive: false,
      };
    }

    // Parse format like "09:00–19:30" or "09:00-19:30"
    const timeMatch = timeString.match(
      /(\d{2}:\d{2})[\s]*[–-][\s]*(\d{2}:\d{2})/,
    );
    if (timeMatch) {
      return {
        startTime: timeMatch[1],
        endTime: timeMatch[2],
        isActive: true,
      };
    }

    // If format is different, return default active hours
    return {
      startTime: '09:00',
      endTime: '18:00',
      isActive: true,
    };
  }

  private async createWorkingHours(
    office: OfficeEntity,
    csvData: CSVOfficeData,
  ): Promise<void> {
    const dayMappings = {
      MONDAY: csvData.Lundi,
      TUESDAY: csvData.Mardi,
      WEDNESDAY: csvData.Mercredi,
      THURSDAY: csvData.Jeudi,
      FRIDAY: csvData.Vendredi,
      SATURDAY: csvData.Samedi,
      SUNDAY: csvData.Dimanche,
    };

    for (const [dayOfWeek, timeString] of Object.entries(dayMappings)) {
      const { startTime, endTime, isActive } =
        this.parseWorkingHours(timeString);

      const workingHour = this.repositoryWorkingHour.create({
        office: office,
        dayOfWeek,
        fromTime: startTime,
        toTime: endTime,
        isActive,
        hasBreak: false,
      });

      await this.repositoryWorkingHour.save(workingHour);
    }
  }

  private async createDefaultRates(office: OfficeEntity): Promise<void> {
    const [madCurrency, usdCurrency, eurCurrency] = await Promise.all([
      this.repositoryCurrency.findOne({ where: { code: 'MAD' } }),
      this.repositoryCurrency.findOne({ where: { code: 'USD' } }),
      this.repositoryCurrency.findOne({ where: { code: 'EUR' } }),
    ]);

    if (!madCurrency || !usdCurrency || !eurCurrency) {
      throw new Error('Required currencies not found');
    }

    // Create USD rate
    const officeUsdRate = this.repositoryOfficeRate.create({
      office: office,
      baseCurrency: madCurrency,
      targetCurrency: usdCurrency,
      buyRate: faker.number.float({ min: 9.5, max: 10.5, fractionDigits: 2 }),
      sellRate: faker.number.float({ min: 10.0, max: 11.0, fractionDigits: 2 }),
      isActive: true,
    });

    // Create EUR rate
    const officeEurRate = this.repositoryOfficeRate.create({
      office: office,
      baseCurrency: madCurrency,
      targetCurrency: eurCurrency,
      buyRate: 11.0,
      sellRate: 11.5,
      isActive: true,
    });

    await Promise.all([
      this.repositoryOfficeRate.save(officeUsdRate),
      this.repositoryOfficeRate.save(officeEurRate),
    ]);
  }

  private async createImages(csvData: CSVOfficeData): Promise<FileEntity[]> {
    const images =
      csvData['Image 1'] || csvData['Image 2'] || csvData['Image 3'];
    const imagesObjects: FileEntity[] = [];
    if (images) {
      const imagePaths = images.split(',').map((path) => path.trim());
      for (const imagePath of imagePaths) {
        const file = await this.repositoryFile.create({
          path: imagePath,
        });
        imagesObjects.push(file);
      }
    }
    return imagesObjects;
  }

  private async ensureUniqueOffice(
    officeData: Partial<OfficeEntity>,
    maxRetries: number = 5,
  ): Promise<OfficeEntity> {
    let attempts = 0;

    while (attempts < maxRetries) {
      try {
        const office = this.repositoryOffice.create(officeData);
        return await this.repositoryOffice.save(office);
      } catch (error: any) {
        attempts++;

        if (error.code === '23505' && attempts < maxRetries) {
          console.log(
            `Duplicate detected, modifying office name (attempt ${attempts}/${maxRetries})`,
          );

          // Modify the office name to make it unique
          officeData.officeName = `${officeData.officeName} - ${attempts}`;

          continue;
        }

        throw error;
      }
    }

    throw new Error(
      `Failed to create unique office after ${maxRetries} attempts`,
    );
  }

  async createOfficeFromCSV(csvData: CSVOfficeData): Promise<OfficeEntity> {
    // Get Morocco country
    const country = await this.repositoryCountry.findOne({
      where: { alpha2: 'MA' },
    });

    if (!country) {
      throw new Error('Country Morocco not found');
    }

    // Get or create city
    let city = await this.repositoryCity.findOne({
      where: { name: csvData.Ville.toLowerCase() },
    });

    if (!city) {
      console.log(`Creating city: ${csvData.Ville}`);
      city = this.repositoryCity.create({
        name: csvData.Ville.toLowerCase(),
        country: country,
      });
      city = await this.repositoryCity.save(city);
    }

    // Get random user as owner
    const usersCount = await this.repositoryUser.count();
    if (usersCount === 0) {
      throw new Error('No users found. Please seed users first.');
    }

    const randomUser = await this.repositoryUser.findOne({
      where: { id: Math.floor(Math.random() * usersCount) + 1 },
    });

    if (!randomUser) {
      throw new Error('User not found');
    }

    // Parse coordinates (flip longitude and latitude)
    const longitude = parseFloat(csvData.Latitude) || 0;
    const latitude = parseFloat(csvData.Longitude) || 0;

    // Clean phone number
    const cleanPhone = csvData['Téléphone']?.replace(/\s/g, '') || '';

    // create images
    const images = await this.createImages(csvData);

    // Prepare office data
    const officeData = {
      officeName: csvData['Bureau de change']?.trim() || 'Unknown Office',
      registrationNumber: `REG-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      currencyExchangeLicenseNumber: `LIC-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      address: csvData.Adresse?.trim() || '',
      postalCode: csvData['Code postal']?.trim() || '',
      city: city,
      country: country,
      primaryPhoneNumber: cleanPhone,
      owner: randomUser,
      whatsappNumber: cleanPhone,
      website: csvData['Site Web/Email']?.trim() || null,
      googleMapsLink: csvData.Lien?.trim() || null,
      logo: null,
      images: images,
      location: {
        type: 'Point',
        coordinates: [longitude, latitude],
      },
    };

    // Create office with retry logic
    const savedOffice = await this.ensureUniqueOffice(
      officeData as Partial<OfficeEntity>,
    );

    // Create working hours
    await this.createWorkingHours(savedOffice, csvData);

    // Create default rates
    await this.createDefaultRates(savedOffice);

    return savedOffice;
  }

  async createOfficesFromCSV(csvFilePath: string): Promise<OfficeEntity[]> {
    console.log(`Reading CSV file: ${csvFilePath}`);

    const csvData = await this.parseCSVData(csvFilePath);
    console.log(`Found ${csvData.length} offices in CSV`);

    const offices: OfficeEntity[] = [];
    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < csvData.length; i++) {
      const row = csvData[i];

      try {
        // Skip rows with empty office names
        if (!row['Bureau de change']?.trim()) {
          console.log(`Skipping row ${i + 1}: Empty office name`);
          continue;
        }

        console.log(
          `Creating office ${i + 1}/${csvData.length}: ${row['Bureau de change']}`,
        );

        const office = await this.createOfficeFromCSV(row);
        offices.push(office);
        successCount++;

        // Log progress
        if (successCount % 5 === 0) {
          console.log(`Progress: ${successCount} offices created successfully`);
        }
      } catch (error: any) {
        errorCount++;
        console.error(
          `Error creating office from row ${i + 1}:`,
          error.message,
        );

        // Continue with next office instead of stopping
        continue;
      }
    }

    console.log(`\n=== CSV IMPORT COMPLETE ===`);
    console.log(`Successfully created: ${successCount} offices`);
    console.log(`Errors encountered: ${errorCount}`);
    console.log(`Total processed: ${csvData.length} rows`);

    return offices;
  }
}
