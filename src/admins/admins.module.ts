import {
  // common
  Module,
} from '@nestjs/common';
import { AdminsService } from './admins.service';
import { <PERSON>minsController } from './admins.controller';
import { OfficesModule } from '../offices/offices.module';
import { AnalyticsModule } from '../analytics/analytics.module';
import { AlertsModule } from '../alerts/alerts.module';
import { RateHistoriesModule } from '../rate-histories/rate-histories.module';
import { ProfileViewsModule } from '../profile-views/profile-views.module';

@Module({
  imports: [
    // import modules, etc.
    OfficesModule,
    AnalyticsModule,
    AlertsModule,
    RateHistoriesModule,
    ProfileViewsModule,
  ],
  controllers: [AdminsController],
  providers: [AdminsService],
  exports: [AdminsService],
})
export class AdminsModule {}
