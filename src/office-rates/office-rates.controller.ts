import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
} from '@nestjs/common';
import { OfficeRatesService } from './office-rates.service';
import { CreateOfficeRateDto } from './dto/create-office-rate.dto';
import { UpdateOfficeRateDto } from './dto/update-office-rate.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { OfficeRate } from './domain/office-rate';
import { AuthGuard } from '@nestjs/passport';
// import {
//   InfinityPaginationResponse,
//   InfinityPaginationResponseDto,
// } from '../utils/dto/infinity-pagination-response.dto';
// import { infinityPagination } from '../utils/infinity-pagination';
// import { FindAllOfficeRatesDto } from './dto/find-all-office-rates.dto';

@ApiTags('Officerates')
@ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
@Controller({
  path: 'office-rates',
  version: '1',
})
export class OfficeRatesController {
  constructor(private readonly officeRatesService: OfficeRatesService) {}

  @Post()
  @ApiCreatedResponse({
    type: OfficeRate,
  })
  create(@Body() createOfficeRateDto: CreateOfficeRateDto, @Request() req) {
    return this.officeRatesService.create({
      ...createOfficeRateDto,
      owner: req.user.id,
    });
  }

  // @Get()
  // @ApiOkResponse({
  //   type: InfinityPaginationResponse(OfficeRate),
  // })
  // async findAll(
  //   @Query() query: FindAllOfficeRatesDto,
  // ): Promise<InfinityPaginationResponseDto<OfficeRate>> {
  //   const page = query?.page ?? 1;
  //   let limit = query?.limit ?? 10;
  //   if (limit > 50) {
  //     limit = 50;
  //   }

  //   return infinityPagination(
  //     await this.officeRatesService.findAllWithPagination({
  //       paginationOptions: {
  //         page,
  //         limit,
  //       },
  //     }),
  //     { page, limit },
  //   );
  // }

  @Get()
  async getOfficeRatesByOfficeId(@Request() req) {
    return this.officeRatesService.getOfficeRatesByOfficeId(req.user.id);
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: OfficeRate,
  })
  findById(@Param('id') id: string) {
    return this.officeRatesService.findById(id);
  }

  @Patch(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  @ApiOkResponse({
    type: OfficeRate,
  })
  update(
    @Param('id') id: string,
    @Body() updateOfficeRateDto: UpdateOfficeRateDto,
  ) {
    return this.officeRatesService.update(id, updateOfficeRateDto);
  }

  @Delete(':id')
  @ApiParam({
    name: 'id',
    type: String,
    required: true,
  })
  remove(@Param('id') id: string) {
    return this.officeRatesService.remove(id);
  }
}
