{"name": "exchango", "version": "1.0.0", "description": "Exchango is a currency exchange platform", "author": "<PERSON>", "private": true, "scripts": {"docker": "docker compose up -d postgres maildev adminer", "typeorm": "env-cmd ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "migration:generate": "npm run typeorm -- --dataSource=src/database/data-source.ts migration:generate", "postmigration:generate": "npm run lint -- --fix", "migration:create": "npm run typeorm -- migration:create", "migration:run": "npm run typeorm -- --dataSource=src/database/data-source.ts migration:run", "migration:revert": "npm run typeorm -- --dataSource=src/database/data-source.ts migration:revert", "schema:drop": "npm run typeorm -- --dataSource=src/database/data-source.ts schema:drop", "seed:create:relational": "hygen seeds create-relational", "generate:resource:relational": "hygen generate relational-resource", "postgenerate:resource:relational": "npm run lint -- --fix", "add:property:to-relational": "hygen property add-to-relational", "postadd:property:to-relational": "npm run lint -- --fix", "seed:run": "ts-node -r tsconfig-paths/register ./src/database/seeds/relational/run-seed.ts", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:swc": "nest start -b swc -w", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "start:prod:pm2": "pm2 start dist/main.js --name exchango", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "env-cmd jest --config ./test/jest-e2e.json", "test:e2e:relational:docker": "docker compose -f docker-compose.relational.test.yaml --env-file env-example-relational -p tests up -d --build && docker compose -f docker-compose.relational.test.yaml -p tests exec api /opt/wait-for-it.sh -t 0 localhost:3000 -- npm run test:e2e -- --watchAll --runInBand && docker compose -f docker-compose.relational.test.yaml -p tests down && docker compose -p tests rm -svf", "prepare": "is-ci || husky", "release": "release-it"}, "dependencies": {"@aws-sdk/client-s3": "3.758.0", "@aws-sdk/s3-request-presigner": "3.758.0", "@date-fns/tz": "^1.2.0", "@nestjs/common": "11.0.15", "@nestjs/config": "4.0.2", "@nestjs/core": "11.0.15", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "11.0.0", "@nestjs/passport": "11.0.5", "@nestjs/platform-express": "11.0.15", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "11.1.1", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "11.0.0", "@types/geojson": "^7946.0.16", "@types/multer-s3": "3.0.3", "apple-signin-auth": "1.7.8", "bcryptjs": "3.0.2", "class-transformer": "0.5.1", "class-validator": "0.14.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "16.5.0", "fb": "2.0.0", "google-auth-library": "9.15.1", "handlebars": "4.7.8", "helmet": "^8.1.0", "js-yaml": "^4.1.0", "ms": "2.1.3", "multer": "1.4.5-lts.2", "multer-s3": "3.0.1", "nest-winston": "^1.10.2", "nestjs-i18n": "10.5.1", "nestjs-paginate": "^12.5.0", "nodemailer": "6.10.0", "papaparse": "^5.5.3", "passport": "0.7.0", "passport-anonymous": "1.0.1", "passport-jwt": "4.0.1", "pg": "8.14.1", "reflect-metadata": "0.2.2", "rimraf": "6.0.1", "rxjs": "7.8.2", "slugify": "^1.6.6", "source-map-support": "0.5.21", "swagger-ui-express": "5.0.1", "typeorm": "0.3.22", "ultramsg-whatsapp-api": "^1.0.2", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "winston-loki": "^6.1.3"}, "devDependencies": {"@commitlint/cli": "19.8.0", "@commitlint/config-conventional": "19.8.0", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.20.0", "@faker-js/faker": "^9.7.0", "@nestjs/cli": "11.0.6", "@nestjs/schematics": "11.0.5", "@nestjs/testing": "11.0.15", "@release-it/conventional-changelog": "10.0.0", "@swc/cli": "0.6.0", "@swc/core": "1.11.18", "@types/bcryptjs": "3.0.0", "@types/express": "5.0.1", "@types/facebook-js-sdk": "3.3.12", "@types/jest": "29.5.14", "@types/js-yaml": "^4.0.9", "@types/ms": "2.1.0", "@types/multer": "1.4.12", "@types/node": "22.13.10", "@types/papaparse": "^5.3.16", "@types/passport-anonymous": "1.0.5", "@types/passport-jwt": "4.0.1", "@types/supertest": "6.0.3", "@typescript-eslint/eslint-plugin": "8.29.1", "@typescript-eslint/parser": "8.29.1", "env-cmd": "10.1.0", "eslint": "9.24.0", "eslint-config-prettier": "10.1.2", "eslint-plugin-prettier": "5.2.6", "globals": "^16.0.0", "husky": "9.1.7", "hygen": "6.2.11", "is-ci": "4.1.0", "jest": "29.7.0", "prettier": "3.5.3", "prompts": "2.4.2", "release-it": "18.1.2", "supertest": "7.1.0", "ts-jest": "29.3.1", "ts-loader": "9.5.2", "ts-node": "10.9.2", "tsconfig-paths": "4.2.0", "tslib": "2.8.1", "typescript": "5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "release-it": {"git": {"commitMessage": "chore: release v${version}"}, "github": {"release": true}, "npm": {"publish": false}, "plugins": {"@release-it/conventional-changelog": {"infile": "CHANGELOG.md", "preset": {"name": "conventionalcommits", "types": [{"type": "chore(deps)", "section": "Dependency Upgrades"}, {"type": "fix(deps)", "section": "Dependency Upgrades"}, {"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "perf", "section": "Performance Improvements"}, {"type": "revert", "section": "Reverts"}, {"type": "docs", "section": "Documentation"}, {"type": "refactor", "section": "Code Refactoring"}, {"type": "test", "section": "Tests"}, {"type": "ci", "section": "Continuous Integration"}]}}}}}